/**
 * RPC 客户端 - 简化的 API 接口
 * 提供高级封装，隐藏复杂的管理逻辑
 */
import { rpcManager } from "./RpcManager";

// 初始化标志
let isInitialized = false;

/**
 * 初始化 RPC 客户端
 */
export async function initializeRpcClient() {
  if (isInitialized) return;

  const providerUrls = JSON.parse(process.env.NEXT_PUBLIC_PROVIDER_URLS || "[]");

  if (!Array.isArray(providerUrls) || providerUrls.length === 0) {
    throw new Error("No RPC provider URLs configured");
  }

  await rpcManager.initialize(providerUrls);
  isInitialized = true;
}

/**
 * 通用 RPC 调用
 */
export async function callRpc<T = any>(method: string, params: any[] = []): Promise<T> {
  await initializeRpcClient();
  return rpcManager.executeRequest<T>(method, params);
}

/**
 * 常用的 RPC 方法封装
 */
export const rpcMethods = {
  // 获取链 ID
  getChainId: () => callRpc<string>("eth_chainId"),

  // 获取区块号
  getBlockNumber: () => callRpc<string>("eth_blockNumber"),

  // 获取余额
  getBalance: (address: string, blockTag = "latest") => callRpc<string>("eth_getBalance", [address, blockTag]),

  // 获取交易数量
  getTransactionCount: (address: string, blockTag = "latest") =>
    callRpc<string>("eth_getTransactionCount", [address, blockTag]),

  // 调用合约
  call: (transaction: any, blockTag = "latest") => callRpc<string>("eth_call", [transaction, blockTag]),

  // 估算 Gas
  estimateGas: (transaction: any) => callRpc<string>("eth_estimateGas", [transaction]),

  // 获取交易回执
  getTransactionReceipt: (txHash: string) => callRpc<any>("eth_getTransactionReceipt", [txHash]),

  // 发送原始交易
  sendRawTransaction: (signedTx: string) => callRpc<string>("eth_sendRawTransaction", [signedTx]),

  // 获取区块信息
  getBlock: (blockHashOrNumber: string, includeTransactions = false) =>
    callRpc<any>("eth_getBlockByHash", [blockHashOrNumber, includeTransactions]),

  // 获取交易信息
  getTransaction: (txHash: string) => callRpc<any>("eth_getTransactionByHash", [txHash]),

  // 获取日志
  getLogs: (filter: any) => callRpc<any[]>("eth_getLogs", [filter]),
};

/**
 * 批量 RPC 调用
 */
export async function batchRpcCall(
  calls: Array<{
    method: string;
    params: any[];
    id?: string | number;
  }>,
): Promise<any[]> {
  await initializeRpcClient();

  // 并发执行多个调用
  const promises = calls.map(call => rpcManager.executeRequest(call.method, call.params));

  return Promise.all(promises);
}

/**
 * 获取 RPC 状态
 */
export function getRpcStatus() {
  return rpcManager.getNodesStatus();
}

/**
 * 获取详细的 RPC 状态报告（用于调试）
 */
export function getRpcDetailedStatus() {
  return rpcManager.getDetailedStatus();
}

/**
 * 创建 ethers.js 兼容的 Provider
 */
export class RpcProvider {
  // 添加 provider 属性以兼容 ContractRunner 接口
  provider = this;
  async send(method: string, params: any[]): Promise<any> {
    return callRpc(method, params);
  }

  async getNetwork() {
    const chainId = await this.send("eth_chainId", []);
    return {
      chainId: parseInt(chainId, 16),
      name: "unknown",
    };
  }

  async getBlockNumber(): Promise<number> {
    const blockNumber = await this.send("eth_blockNumber", []);
    return parseInt(blockNumber, 16);
  }

  async getBalance(address: string, blockTag = "latest"): Promise<string> {
    return this.send("eth_getBalance", [address, blockTag]);
  }

  async getTransactionCount(address: string, blockTag = "latest"): Promise<number> {
    const count = await this.send("eth_getTransactionCount", [address, blockTag]);
    return parseInt(count, 16);
  }

  async call(transaction: any, blockTag = "latest"): Promise<string> {
    return this.send("eth_call", [transaction, blockTag]);
  }

  async estimateGas(transaction: any): Promise<string> {
    return this.send("eth_estimateGas", [transaction]);
  }

  async sendTransaction(signedTx: string): Promise<string> {
    return this.send("eth_sendRawTransaction", [signedTx]);
  }

  async getTransactionReceipt(txHash: string): Promise<any> {
    return this.send("eth_getTransactionReceipt", [txHash]);
  }

  async waitForTransaction(txHash: string, confirmations = 1, timeout = 0): Promise<any> {
    const startTime = Date.now();

    while (true) {
      const receipt = await this.getTransactionReceipt(txHash);

      if (receipt && receipt.blockNumber) {
        const currentBlock = await this.getBlockNumber();
        const confirmationCount = currentBlock - parseInt(receipt.blockNumber, 16) + 1;

        if (confirmationCount >= confirmations) {
          return receipt;
        }
      }

      if (timeout > 0 && Date.now() - startTime > timeout) {
        throw new Error("Transaction timeout");
      }

      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

/**
 * 创建 Provider 实例
 */
export function createRpcProvider(): RpcProvider {
  return new RpcProvider();
}
