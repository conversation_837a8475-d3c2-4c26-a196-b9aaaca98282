import { getItem } from "@/utils";
import Decimal from "decimal.js";

function isWithinOneDays(createdAt: string): boolean {
  const createdDate = new Date(createdAt);
  const currentDate = new Date();

  const timeDifference = currentDate.getTime() - createdDate.getTime();
  const daysDifference = timeDifference / (1000 * 60 * 60 * 24);

  return daysDifference < 1;
}

const calculateTotalForAsks = (asks: any[]) => {
  if (asks.length === 0) {
    return { asks: [], maxBidsTotal: "0.00" };
  }
  asks.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));

  let accumulatedTotal = 0;
  for (let i = asks.length - 1; i >= 0; i--) {
    const ask = asks[i];
    const currentTotal = parseFloat(ask.price) * parseFloat(ask.size);
    accumulatedTotal += currentTotal;
    ask.total = accumulatedTotal.toFixed(2);
  }
  const maxAsksTotal = accumulatedTotal.toFixed(2);

  return { asks, maxAsksTotal };
};

const calculateTotalForBids = (bids: any[]) => {
  if (bids.length === 0) {
    return { bids: [], maxBidsTotal: "0.00" };
  }
  bids.sort((a, b) => parseFloat(b.price) - parseFloat(a.price));
  let accumulatedTotal = 0;

  const updatedBids = bids.map(bid => {
    const currentTotal = parseFloat(bid.price) * parseFloat(bid.size);
    accumulatedTotal += currentTotal;
    return {
      ...bid,
      total: accumulatedTotal.toFixed(2),
    };
  });

  const maxBidsTotal = accumulatedTotal.toFixed(2);
  return { bids: updatedBids, maxBidsTotal };
};

function generateOrderSalt(): string {
  return Math.round(Math.random() * Date.now()) + "";
}

function calculateTimeToNow(isoString: string, isUTC = false): string {
  // Get current UTC timestamp (milliseconds)
  const nowInMs = Date.now();
  // Convert ISO time string to timestamp (milliseconds)
  const timestampInMs = new Date(isoString).getTime();

  let diff: number;

  if (isUTC) {
    // For UTC time, calculate difference directly
    diff = nowInMs - timestampInMs;
  } else {
    const timezoneOffsetInMs = new Date().getTimezoneOffset() * 60 * 1000;
    diff = nowInMs - (timestampInMs - timezoneOffsetInMs);
  }

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  const month = 30 * day;
  const year = 365 * day;

  const units = [
    { max: hour, value: minute, name: "minute", plural: "minutes" },
    { max: day, value: hour, name: "hour", plural: "hours" },
    { max: week, value: day, name: "day", plural: "days" },
    { max: month, value: week, name: "week", plural: "weeks" },
    { max: year, value: month, name: "month", plural: "months" },
    { max: Infinity, value: year, name: "year", plural: "years" },
  ];

  // Handle very recent time
  if (diff < minute) {
    return "just now";
  }

  // Find appropriate unit and calculate
  for (const unit of units) {
    if (diff < unit.max) {
      const count = Math.floor(diff / unit.value);
      return count <= 1 ? `1 ${unit.name} ago` : `${count} ${unit.plural} ago`;
    }
  }

  return "unknown time";
}

function calculateOrderFilled(side: string, size_matched: string, original_size: string, price: string) {
  let currentFilled = "";
  const sizeMatchedNum = parseFloat(size_matched);
  const originalSizeNum = parseFloat(original_size);

  if (side === "BUY") {
    const filledAmount = Math.round(sizeMatchedNum / parseFloat(price) / 10 ** 6);
    const totalAmount = Math.round(originalSizeNum / parseFloat(price) / 10 ** 6);

    currentFilled = `${Math.round(filledAmount)} / ${totalAmount}`;
  } else if (side === "SELL") {
    const filledAmount = Math.round(sizeMatchedNum / 10 ** 6);
    const totalAmount = Math.round(originalSizeNum / 10 ** 6);

    currentFilled = `${filledAmount} / ${totalAmount}`;
  }
  return currentFilled;
}

function calculateOrderTotal(side: string, size_current: string, price: string) {
  let totalValue = 0;
  const originalSizeNum = parseFloat(size_current);
  const priceNum = parseFloat(price);

  if (side === "BUY") {
    totalValue = parseFloat((priceNum * (originalSizeNum / 10 ** 6 / priceNum)).toFixed(2));
  } else if (side === "SELL") {
    totalValue = parseFloat((priceNum * (originalSizeNum / 10 ** 6)).toFixed(2));
  }
  return totalValue;
}

const calculateOrderPayment = (price: number, sharesValue: number): number => {
  const priceDecimal = new Decimal(price);
  const sharesDecimal = new Decimal(sharesValue);

  return priceDecimal
    .mul(sharesDecimal)
    .mul(10 ** 6)
    .div(100)
    .toNumber();
};

/**
 * Calculate the total value of an order including the fee.
 * @param side The order side (BUY or SELL).
 * @param size_current unmatched size of the order.
 * @param price The price of the order.
 * @param feeRate The fee rate, default is 2%.
 * @returns The total value of the order including the fee.
 */
function calculateOrderTotalWithFee(side: string, size_current: string, price: string, feeRate = 0.02): number {
  const orderTotal = calculateOrderTotal(side, size_current, price);
  return orderTotal * (1 + feeRate);
}

function base64DecodeByLanguage(encodedString: string, lng?: string): string {
  const currentLanguage = lng ? lng : getItem("current_language");

  try {
    const result = Buffer.from(encodedString, "base64").toString("utf-8");
    const parsedResult = parseContentByLanguage(result, currentLanguage);
    if (parsedResult) {
      return parsedResult;
    }
    try {
      return JSON.parse(result)[0].content;
    } catch (jsonError) {
      return "";
    }
  } catch (error) {
    console.error("Error decoding Base64 string:", error);
    return "";
  }
}

function base64DecodeByEn(encodedString: string): string {
  try {
    const result = Buffer.from(encodedString, "base64").toString("utf-8");
    const parsedResult = parseContentByLanguage(result, "en");
    const cleanedResult = parsedResult.replace(/\?/g, ""); // 移除问号
    return cleanedResult.replace(/\s+/g, "-"); // 替换空格为中划线
  } catch (error) {
    console.error("Error decoding Base64 string:", error);
    return "";
  }
}

function parseRulesContent(encodedString: string, lng?: string): string {
  const currentLanguage = lng ? lng : getItem("current_language");

  try {
    const decodedBytes = Uint8Array.from(atob(encodedString), c => c.charCodeAt(0));
    const decodedString = new TextDecoder("utf-8").decode(decodedBytes);

    // 尝试解析为多语言JSON格式
    const parsedResult = parseContentByLanguage(decodedString, currentLanguage);
    if (parsedResult) {
      return parsedResult;
    }

    // 如果不是多语言格式，处理转义的换行符后返回
    return decodedString.replace(/\\n/g, '\n').replace(/\\r/g, '\r');
  } catch (error) {
    console.error("Failed to parse encoded string:", error);
    return "";
  }
}

function parseContentByLanguage(jsonString: string, language: string): string {
  if (typeof jsonString !== "string" || (!jsonString.trim().startsWith("{") && !jsonString.trim().startsWith("["))) {
    return "";
  }

  try {
    const contentArray: any = JSON.parse(jsonString);
    const contentItem = contentArray.find((item: any) => item.language === language);
    if (contentItem && contentItem.content) {
      // 处理转义的换行符
      return contentItem.content.replace(/\\n/g, '\n').replace(/\\r/g, '\r');
    }
    return "";
  } catch (error) {
    console.error("Error parsing JSON string:", error);
    return "";
  }
}

const findOrderInformation = (informationList: any[], asset_id: string) => {
  if (!Array.isArray(informationList)) {
    return undefined;
  }

  return informationList.find((info: any) => {
    try {
      return Array.isArray(info.clob_token_ids) && info.clob_token_ids.includes(asset_id);
    } catch (e) {
      console.error("Invalid JSON in clobTokenIds:", info.clob_token_ids);
      return false;
    }
  });
};

const findOrderAmounts = (orderAmountList: any[], itemId: string) => {
  const orderAmount = orderAmountList.find((order: any) => order.order_id == itemId);

  return {
    makerAmount: orderAmount?.makeramount || 0,
    makerAmountOriginal: orderAmount?.makeramountoriginal || 0,
    takerAmount: orderAmount?.takeramount || 0,
    takerAmountOriginal: orderAmount?.takeramountoriginal || 0,
    originPrice: orderAmount?.original_price || 0,
  };
};

const getTotalVolumeClob = (eventMarkets: any) => {
  return eventMarkets?.reduce((total: any, market: any) => {
    return total + (market.question_market?.volumeclob || 0);
  }, 0);
};

export {
  isWithinOneDays,
  calculateTimeToNow,
  calculateTotalForAsks,
  calculateTotalForBids,
  generateOrderSalt,
  calculateOrderFilled,
  calculateOrderTotal,
  calculateOrderTotalWithFee,
  calculateOrderPayment,
  base64DecodeByLanguage,
  base64DecodeByEn,
  findOrderInformation,
  findOrderAmounts,
  parseRulesContent,
  getTotalVolumeClob,
};
