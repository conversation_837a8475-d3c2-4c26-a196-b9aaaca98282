import React, { useEffect, useState } from "react";
import { parseRulesContent } from "@/utils";
import { Divider } from "@heroui/divider";
import { <PERSON><PERSON>, <PERSON> } from "@heroui/react";

interface RulesItemProps {
  currentEvent: {
    rules?: string;
    event_markets?: {
      question_market?: {
        id?: string;
        uma_url?: string;
      };
    }[];
  };
  selectedItemId?: string | null;
}

const RulesItem: React.FC<RulesItemProps> = ({ currentEvent, selectedItemId }) => {
  const [currentRules, setCurrentRules] = useState<string>("");
  const [isExpanded, setIsExpanded] = useState<boolean>(false);

  // 如果只有一个question，默认展示第一个；否则根据选中状态匹配
  const getUmaUrl = () => {
    const markets = currentEvent?.event_markets || [];

    if (markets.length === 1) {
      // 只有一个question时，直接使用第一个
      return markets[0]?.question_market?.uma_url || null;
    } else {
      // 多个question时，根据选中状态匹配
      const matchedMarket = markets.find(market => market.question_market?.id === selectedItemId);
      return matchedMarket?.question_market?.uma_url || null;
    }
  };

  const uma_url = getUmaUrl();

  const getTransactionHash = (url: string | null): string | null => {
    if (!url) return null;
    const match = url.match(/transactionHash=([^&]+)/);
    return match ? `${match[1].slice(0, 12)}...` : null;
  };

  const transactionHash = getTransactionHash(uma_url);

  useEffect(() => {
    if (currentEvent?.rules) {
      try {
        const decodedRules = parseRulesContent(currentEvent.rules);
        setCurrentRules(decodedRules);
      } catch (error) {
        console.error("Error decoding rules content:", error);
        setCurrentRules("Error decoding rules content.");
      }
    }
  }, [currentEvent?.rules]);

  const toggleExpand = () => setIsExpanded(prev => !prev);

  return (
    <div>
      <Divider />
      <div className="text-2xl font-bold mt-6 mb-2">Rules</div>
      <div className={isExpanded ? "" : "line-clamp-3"}>
        {/* 使用纯文本显示，保留换行符 */}
        <div
          style={{
            whiteSpace: "pre-wrap",
            wordWrap: "break-word",
            lineHeight: "1.6",
            fontSize: "16px",
          }}
          className={isExpanded ? "" : "overflow-hidden"}
        >
          {currentRules}
        </div>

        {/* 展开/收起按钮 */}
        {currentRules && currentRules.length > 200 && (
          <Button variant="light" size="sm" onPress={toggleExpand} className="mt-2 text-blue-600 hover:text-blue-800">
            {isExpanded ? "Show Less" : "Show More"}
          </Button>
        )}

        {uma_url && (
          <div className="px-4 py-2 flex items-center justify-between border-1 border-gray-200 rounded-lg gap-4">
            <div className="flex items-center gap-4">
              <div className="font-bold text-xl font-serif text-red-500">UMA</div>
              <div className="flex flex-col">
                <div className="text-gray-500 text-sm">Resolver</div>
                <Link
                  href={uma_url}
                  isExternal
                  className="flex items-center text-sm bg-white rounded-xl cursor-pointer"
                >
                  {transactionHash}
                </Link>
              </div>
            </div>

            <Button
              as={Link}
              href={uma_url}
              isExternal
              size="md"
              radius="full"
              className="bg-transparent border-1 hover:opacity-60 text-md font-semibold my-1 px-4"
            >
              Propose resolution
            </Button>
          </div>
        )}
      </div>
      <Button
        onPress={toggleExpand}
        size="md"
        radius="full"
        className="flex-1 bg-gray-200 hover:opacity-60 text-md font-semibold my-4"
      >
        {isExpanded ? "Show Less" : "Show More"}
      </Button>
    </div>
  );
};

export default RulesItem;
