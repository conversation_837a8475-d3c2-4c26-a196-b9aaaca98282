"use client";

import React, { useCallback, useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import VideoModal from "./learning-center/VideoModal";
// import { HeaderMenuLinks } from "./navbar/MenuLinks";
import { SearchBar } from "./navbar/SearchBar";
import { SubHeaderNav } from "./navbar/SubHeaderNav";
import { getSubHeaderListData } from "@/api/events";
import { siteConfig } from "@/configs/site";
import { setItem } from "@/utils";
import { ChevronDown, CirclePlay, Globe } from "lucide-react";
import { useTranslation } from "react-i18next";
import { RainbowKitCustomConnectButton } from "~~/components/scaffold-eth";
import { useGlobalState } from "~~/services/store/store";

// 地区选择项类型
interface RegionItem {
  id: string;
  label: string;
  region: string;
  type: string;
}

export const Header = () => {
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false);

  // 地区选择相关状态
  const [regionItems, setRegionItems] = useState<RegionItem[]>([]);
  const [currentRegionText, setCurrentRegionText] = useState("Select Region");
  const [isRegionDropdownOpen, setIsRegionDropdownOpen] = useState(false);
  const [isRegionLoading, setIsRegionLoading] = useState(false);

  const { i18n } = useTranslation();
  const { current_language } = useGlobalState().nativeCurrency;
  const setCurrentLanguage = useGlobalState(state => state.setCurrentLanguage);

  // How it works 视频数据
  const howItWorksVideo = {
    id: "UmVHNQ7QDcU",
    title: "How PredictOne Works",
    description: "Learn how to use PredictOne prediction market platform in this quick tutorial.",
    embedUrl: "https://www.youtube.com/embed/UmVHNQ7QDcU",
  };

  const handleHowItWorksClick = () => {
    setIsVideoModalOpen(true);
  };

  const handleCloseVideoModal = () => {
    setIsVideoModalOpen(false);
  };

  useEffect(() => {
    const fetchRegionData = async () => {
      try {
        const res = await getSubHeaderListData();
        const navItemsData = res.data.sub_header_nav;
        const allRegions = navItemsData.filter((item: any) => item.type === "normal");

        const regionOrder = ["en", "zh", "ko"];

        // 按照固定顺序排列地区
        const sortedRegions = regionOrder
          .map(regionCode => allRegions.find((item: any) => item.region === regionCode))
          .filter(Boolean); // 过滤掉未找到的地区

        setRegionItems(sortedRegions);

        // 设置当前地区文本
        const currentRegion = sortedRegions.find((item: any) => item.region === current_language);
        if (currentRegion) {
          setCurrentRegionText(currentRegion.label);
        } else {
          setCurrentRegionText("Select Region");
        }
      } catch (error) {
        console.error("Failed to fetch region data:", error);
      }
    };

    fetchRegionData();
  }, [current_language]);

  // 处理地区选择
  const handleRegionSelect = useCallback(
    async (region: RegionItem) => {
      setIsRegionLoading(true);
      setIsRegionDropdownOpen(false);

      try {
        // 更新存储和全局状态
        setItem("currentNavItem", region);
        setItem("current_language", region.region);
        i18n.changeLanguage(region.region);
        setCurrentLanguage(region.region);
        setCurrentRegionText(region.label);
      } catch (error) {
        console.error("Failed to change region:", error);
      } finally {
        setTimeout(() => {
          setIsRegionLoading(false);
        }, 500);
      }
    },
    [i18n, setCurrentLanguage],
  );

  // 点击外部关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isRegionDropdownOpen && !target.closest(".region-dropdown")) {
        setIsRegionDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isRegionDropdownOpen]);

  return (
    <div className="sticky navbar flex-col top-0 bg-base-100 z-20 px-2 py-0 sm:px-6 border-b border-gray-300">
      <div className="flex w-full min-h-20 items-center md:justify-normal justify-between flex-nowrap">
        <div className="flex-shrink-0">
          <Link href="/" className="flex items-center">
            <Image className="size-12 rounded-full" src="/logo.png" alt="" width={50} height={50} />
            <span className="flex flex-col ml-2 items-start bg-gradient-to-br from-blue-500 to-purple-700 bg-clip-text">
              <div className="text-2xl font-semibold text-transparent">{siteConfig.name}</div>
            </span>
          </Link>
        </div>

        <div className="flex-grow px-2 md:flex hidden items-center space-x-2">
          <SearchBar />

          {/* 地区选择下拉框 */}
          <div className="relative region-dropdown">
            <button
              onClick={() => setIsRegionDropdownOpen(!isRegionDropdownOpen)}
              disabled={isRegionLoading}
              className="flex items-center space-x-2 px-3 py-2 bg-white hover:bg-gray-50 border border-gray-200 hover:border-gray-300 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 font-medium text-sm text-gray-700 whitespace-nowrap min-w-[120px]"
            >
              <Globe size={16} className="text-gray-500" />
              <span className="flex-1 text-left">{isRegionLoading ? "Switching..." : currentRegionText}</span>
              <ChevronDown
                size={16}
                className={`text-gray-400 transition-transform duration-200 ${
                  isRegionDropdownOpen ? "rotate-180" : ""
                }`}
              />
            </button>

            {/* 下拉菜单 */}
            {isRegionDropdownOpen && (
              <div className="absolute top-full left-0 mt-2 w-full bg-white border border-gray-200 rounded-lg shadow-lg z-50 py-1">
                {regionItems.map(region => (
                  <button
                    key={region.id}
                    onClick={() => handleRegionSelect(region)}
                    className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-50 transition-colors duration-150 ${
                      region.region === current_language ? "bg-blue-50 text-blue-600 font-medium" : "text-gray-700"
                    }`}
                  >
                    {region.label}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* How it works 播放图标 */}
          <div className="relative group">
            <button
              onClick={handleHowItWorksClick}
              className="w-8 h-8 bg-white hover:bg-gray-50 border border-gray-200 hover:border-gray-300 rounded-full flex items-center justify-center shadow-sm hover:shadow-md transition-all duration-200"
            >
              <CirclePlay className="w-5 h-5 text-blue-500 hover:text-blue-600" />
            </button>

            {/* Hover 提示文字 - 显示在下方 */}
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 px-3 py-1 bg-gray-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
              How it works
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-800"></div>
            </div>
          </div>
        </div>

        <div className="flex-shrink-0 items-center justify-end md:flex hidden font-medium">
          {/* <HeaderMenuLinks /> */}
          <RainbowKitCustomConnectButton />
        </div>

        {/* 移动端显示 - 添加充值按钮 */}
        <div className="flex-shrink-0 items-center justify-end md:hidden flex font-medium gap-2">
          {/* <HeaderMenuLinks /> */}
          <RainbowKitCustomConnectButton />
        </div>
      </div>

      <div className="w-full">
        <SubHeaderNav />
      </div>

      {/* How it works 视频 */}
      <VideoModal isOpen={isVideoModalOpen} onClose={handleCloseVideoModal} video={howItWorksVideo} />
    </div>
  );
};
