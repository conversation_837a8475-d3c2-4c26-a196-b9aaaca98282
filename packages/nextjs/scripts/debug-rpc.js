/**
 * RPC 调试工具
 * 在浏览器控制台中运行，实时监控RPC节点状态
 */

// 将这些函数添加到全局作用域，方便在控制台中使用
window.debugRpc = {
  /**
   * 显示RPC状态概览
   */
  status: async () => {
    try {
      const { getRpcDetailedStatus } = await import('@/utils/rpc/rpcClient');
      const status = getRpcDetailedStatus();
      
      console.log('🔍 RPC Status Overview');
      console.log('=====================');
      console.log(`📊 Total Nodes: ${status.summary.total}`);
      console.log(`✅ Healthy: ${status.summary.healthy}`);
      console.log(`❌ Unhealthy: ${status.summary.unhealthy}`);
      console.log(`⏰ In Cooldown: ${status.summary.inCooldown}`);
      
      console.log('\n📋 Node Details:');
      console.table(status.nodes.map(node => ({
        URL: node.url.split('/').pop() || node.url,
        Status: node.isHealthy ? '✅ Healthy' : '❌ Unhealthy',
        Priority: node.priority,
        'Success Rate': node.successRate,
        'Avg Response': node.avgResponseTime + 'ms',
        'Failures': node.consecutiveFailures,
        'Cooldown': node.isInCooldown ? '⏰ Yes' : '✅ No',
        'Network': node.networkType,
        'Last Success': node.lastSuccessTime === 'Never' ? 'Never' : 'Recent'
      })));
      
      console.log('\n⚙️ Configuration:');
      console.table(status.config);
      
      return status;
    } catch (error) {
      console.error('❌ Failed to get RPC status:', error);
    }
  },

  /**
   * 监控RPC状态变化
   */
  monitor: async (intervalSeconds = 10) => {
    console.log(`🔄 Starting RPC monitoring (every ${intervalSeconds}s)...`);
    console.log('💡 Use debugRpc.stopMonitor() to stop');
    
    const monitor = async () => {
      try {
        const { getRpcDetailedStatus } = await import('@/utils/rpc/rpcClient');
        const status = getRpcDetailedStatus();
        
        const timestamp = new Date().toLocaleTimeString();
        console.log(`\n⏰ [${timestamp}] RPC Status Update:`);
        
        // 只显示状态变化的节点
        const problematicNodes = status.nodes.filter(node => 
          !node.isHealthy || node.isInCooldown || node.consecutiveFailures > 0
        );
        
        if (problematicNodes.length > 0) {
          console.log('⚠️ Problematic Nodes:');
          console.table(problematicNodes.map(node => ({
            URL: node.url.split('/').pop() || node.url,
            Issue: node.isInCooldown ? '⏰ Cooldown' : 
                   !node.isHealthy ? '❌ Unhealthy' : 
                   node.consecutiveFailures > 0 ? `🔄 ${node.consecutiveFailures} failures` : '✅ OK',
            'Success Rate': node.successRate,
            'Last Success': node.lastSuccessTime === 'Never' ? 'Never' : 'Recent'
          })));
        } else {
          console.log('✅ All nodes are healthy');
        }
        
        console.log(`📊 Summary: ${status.summary.healthy}/${status.summary.total} healthy`);
        
      } catch (error) {
        console.error('❌ Monitor error:', error);
      }
    };
    
    // 立即执行一次
    await monitor();
    
    // 设置定时器
    window.debugRpc._monitorInterval = setInterval(monitor, intervalSeconds * 1000);
  },

  /**
   * 停止监控
   */
  stopMonitor: () => {
    if (window.debugRpc._monitorInterval) {
      clearInterval(window.debugRpc._monitorInterval);
      window.debugRpc._monitorInterval = null;
      console.log('⏹️ RPC monitoring stopped');
    } else {
      console.log('ℹ️ No active monitoring to stop');
    }
  },

  /**
   * 测试特定RPC端点
   */
  test: async (url) => {
    console.log(`🧪 Testing RPC endpoint: ${url}`);
    
    try {
      const startTime = Date.now();
      const response = await fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          jsonrpc: '2.0',
          method: 'eth_chainId',
          params: [],
          id: 1
        })
      });
      
      const responseTime = Date.now() - startTime;
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      
      if (data.error) {
        throw new Error(`RPC Error: ${data.error.message}`);
      }
      
      const chainId = parseInt(data.result, 16);
      console.log(`✅ Test successful:`);
      console.log(`   Response Time: ${responseTime}ms`);
      console.log(`   Chain ID: ${chainId} (${chainId === 8453 ? 'Base Mainnet' : 'Unknown'})`);
      
      return { success: true, responseTime, chainId };
      
    } catch (error) {
      console.log(`❌ Test failed: ${error.message}`);
      return { success: false, error: error.message };
    }
  },

  /**
   * 显示帮助信息
   */
  help: () => {
    console.log('🔧 RPC Debug Tools');
    console.log('==================');
    console.log('debugRpc.status()              - Show current RPC status');
    console.log('debugRpc.monitor(seconds)      - Start monitoring (default: 10s)');
    console.log('debugRpc.stopMonitor()         - Stop monitoring');
    console.log('debugRpc.test(url)             - Test specific RPC endpoint');
    console.log('debugRpc.help()                - Show this help');
    console.log('');
    console.log('💡 Examples:');
    console.log('debugRpc.status()');
    console.log('debugRpc.monitor(5)');
    console.log('debugRpc.test("https://mainnet.base.org")');
  }
};

// 显示初始化信息
console.log('🔧 RPC Debug Tools loaded!');
console.log('💡 Type debugRpc.help() to see available commands');

export default window.debugRpc;
