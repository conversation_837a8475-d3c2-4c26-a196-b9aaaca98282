/**
 * RPC 测试脚本
 * 用于验证新的RPC配置是否正常工作
 */

const rpcUrls = [
  "https://base-pokt.nodies.app",
  "https://1rpc.io/base", 
  "https://base-rpc.publicnode.com",
  "https://mainnet.base.org",
  "https://base.llamarpc.com",
  "https://base.blockpi.network/v1/rpc/public",
  "https://base-mainnet.public.blastapi.io"
];

async function testRpcEndpoint(url) {
  try {
    console.log(`🔍 Testing ${url}...`);
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonrpc: '2.0',
        method: 'eth_chainId',
        params: [],
        id: 1
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.error) {
      throw new Error(`RPC Error: ${data.error.message}`);
    }

    const chainId = parseInt(data.result, 16);
    console.log(`✅ ${url} - Chain ID: ${chainId} (${chainId === 8453 ? 'Base Mainnet' : 'Unknown'})`);
    return { url, success: true, chainId, error: null };
    
  } catch (error) {
    console.log(`❌ ${url} - Error: ${error.message}`);
    return { url, success: false, chainId: null, error: error.message };
  }
}

async function testAllEndpoints() {
  console.log('🚀 Testing RPC endpoints...\n');
  
  const results = [];
  
  for (const url of rpcUrls) {
    const result = await testRpcEndpoint(url);
    results.push(result);
    
    // 添加延迟避免请求过于频繁
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n✅ Working endpoints:');
    successful.forEach(r => console.log(`  - ${r.url}`));
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Failed endpoints:');
    failed.forEach(r => console.log(`  - ${r.url}: ${r.error}`));
  }
  
  console.log('\n💡 Recommendation:');
  if (successful.length >= 3) {
    console.log('✅ You have enough working RPC endpoints for reliable failover.');
  } else if (successful.length >= 1) {
    console.log('⚠️ Consider adding more RPC endpoints for better redundancy.');
  } else {
    console.log('🚨 No working RPC endpoints found! Check your network connection.');
  }
}

// 运行测试
testAllEndpoints().catch(console.error);
