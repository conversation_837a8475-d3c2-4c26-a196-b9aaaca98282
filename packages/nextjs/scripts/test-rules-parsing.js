/**
 * 测试Rules内容解析和换行符处理
 */

// 模拟不同格式的rules数据
const testCases = [
  {
    name: "多语言JSON格式（带换行符）",
    data: btoa(JSON.stringify([
      {
        language: "en",
        content: "Rule 1: This is the first rule.\\nRule 2: This is the second rule.\\n\\nRule 3: This is the third rule with multiple lines."
      },
      {
        language: "zh",
        content: "规则1：这是第一条规则。\\n规则2：这是第二条规则。\\n\\n规则3：这是第三条规则，包含多行内容。"
      }
    ]))
  },
  {
    name: "纯文本格式（带换行符）",
    data: btoa("Rule 1: This is the first rule.\\nRule 2: This is the second rule.\\n\\nRule 3: This is the third rule with multiple lines.")
  },
  {
    name: "多语言JSON格式（真实换行符）",
    data: btoa(JSON.stringify([
      {
        language: "en",
        content: "Rule 1: This is the first rule.\nRule 2: This is the second rule.\n\nRule 3: This is the third rule with multiple lines."
      },
      {
        language: "zh",
        content: "规则1：这是第一条规则。\n规则2：这是第二条规则。\n\n规则3：这是第三条规则，包含多行内容。"
      }
    ]))
  }
];

// 模拟parseRulesContent函数
function parseRulesContent(encodedString, lng = "en") {
  try {
    const decodedBytes = Uint8Array.from(atob(encodedString), c => c.charCodeAt(0));
    const decodedString = new TextDecoder("utf-8").decode(decodedBytes);
    
    // 尝试解析为多语言JSON格式
    const parsedResult = parseContentByLanguage(decodedString, lng);
    if (parsedResult) {
      return parsedResult;
    }
    
    // 如果不是多语言格式，处理转义的换行符后返回
    return decodedString.replace(/\\n/g, '\n').replace(/\\r/g, '\r');
  } catch (error) {
    console.error("Failed to parse encoded string:", error);
    return "";
  }
}

function parseContentByLanguage(jsonString, language) {
  if (typeof jsonString !== "string" || (!jsonString.trim().startsWith("{") && !jsonString.trim().startsWith("["))) {
    return "";
  }

  try {
    const contentArray = JSON.parse(jsonString);
    const contentItem = contentArray.find((item) => item.language === language);
    if (contentItem && contentItem.content) {
      // 处理转义的换行符
      return contentItem.content.replace(/\\n/g, '\n').replace(/\\r/g, '\r');
    }
    return "";
  } catch (error) {
    console.error("Error parsing JSON string:", error);
    return "";
  }
}

// 运行测试
function runTests() {
  console.log('🧪 Testing Rules Content Parsing\n');
  
  testCases.forEach((testCase, index) => {
    console.log(`📋 Test ${index + 1}: ${testCase.name}`);
    console.log('=' .repeat(50));
    
    // 测试英文
    const enResult = parseRulesContent(testCase.data, "en");
    console.log('🇺🇸 English Result:');
    console.log(JSON.stringify(enResult, null, 2));
    console.log('Raw output:');
    console.log(enResult);
    
    // 测试中文
    const zhResult = parseRulesContent(testCase.data, "zh");
    console.log('\n🇨🇳 Chinese Result:');
    console.log(JSON.stringify(zhResult, null, 2));
    console.log('Raw output:');
    console.log(zhResult);
    
    console.log('\n' + '='.repeat(50) + '\n');
  });
  
  // 测试换行符显示
  console.log('🎨 Testing Display with CSS:');
  console.log('For proper display, use CSS: { whiteSpace: "pre-wrap", wordWrap: "break-word" }');
  
  const sampleText = "Line 1\nLine 2\n\nLine 4 after empty line";
  console.log('\nSample text with newlines:');
  console.log('Raw:', JSON.stringify(sampleText));
  console.log('Display:', sampleText);
}

// 在浏览器控制台中运行
if (typeof window !== 'undefined') {
  window.testRulesParsing = runTests;
  console.log('💡 Run testRulesParsing() in the browser console to test rules parsing');
} else {
  // 在Node.js中运行
  runTests();
}

export default runTests;
